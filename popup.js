// DOM Elements
const elements = {
  currentDomain: document.getElementById('current-domain'),
  timeCounter: document.getElementById('time-counter'),
  timeLimitDisplay: document.getElementById('time-limit-display'),
  timeProgress: document.getElementById('time-progress'),
  focusModeBtn: document.getElementById('focus-mode'),
  setLimitBtn: document.getElementById('set-limit'),
  viewStatsBtn: document.getElementById('view-stats'),
  focusModePanel: document.getElementById('focus-mode-panel'),
  pomodoroTimer: document.getElementById('pomodoro-timer'),
  startPomodoroBtn: document.getElementById('start-pomodoro'),
  soundButtons: document.querySelectorAll('.sound-btn'),
  motivationQuote: document.getElementById('motivation-quote')
};

// Create Audio Context for sound generation
let audioContext;
let currentSound = null;

// Sound generators
const sounds = {
  rain: null,
  forest: null,
  waves: null
};

// State management
const state = {
  pomodoro: {
    interval: null,
    minutes: 25,
    seconds: 0,
    isActive: false,
    breakMode: false
  }
};

// Initialize popup
async function initializePopup() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const domain = getDomain(tab.url);
    elements.currentDomain.textContent = domain;

    const { sites = {} } = await chrome.storage.local.get(['sites']);
    const siteData = sites[domain] || { timeSpent: 0, limit: null };
    
    updateTimeDisplay(siteData.timeSpent);
    updateLimitDisplay(siteData.limit);
    updateProgressBar(siteData.timeSpent, siteData.limit);

    // Initialize Web Audio
    initializeAudio();

    // Add event listeners after initialization
    setupEventListeners();
    
    // Update motivational quote
    updateMotivationalQuote();
    
    // Create time limit modal
    createTimeLimitModal();
  } catch (error) {
    console.error('Failed to initialize popup:', error);
    showError('Failed to load site data. Please try again.');
  }
}

// Create time limit modal
function createTimeLimitModal() {
  // Create modal container
  const modal = document.createElement('div');
  modal.className = 'modal';
  modal.id = 'time-limit-modal';
  modal.setAttribute('aria-hidden', 'true');
  
  // Create modal content
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>Set Time Limit</h3>
        <button class="close-btn" aria-label="Close">×</button>
      </div>
      <div class="modal-body">
        <p>Set a daily time limit for <strong id="modal-domain"></strong></p>
        <div class="time-input">
          <input type="number" id="limit-input" min="1" max="1440" value="30">
          <span class="unit">minutes</span>
        </div>
        <div class="time-presets">
          <button data-value="15">15m</button>
          <button data-value="30">30m</button>
          <button data-value="45">45m</button>
          <button data-value="60">1h</button>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" id="cancel-limit">Cancel</button>
        <button class="btn primary" id="save-limit">Save</button>
      </div>
    </div>
  `;
  
  // Add modal to body
  document.body.appendChild(modal);
  
  // Add modal styles
  const style = document.createElement('style');
  style.textContent = `
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }
    
    .modal[aria-hidden="false"] {
      display: flex;
      animation: fadeIn 0.3s ease;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .modal-content {
      background: var(--card-bg);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow-lg);
      width: 90%;
      max-width: 320px;
      overflow: hidden;
      transform: translateY(0);
      animation: slideIn 0.3s ease;
    }
    
    @keyframes slideIn {
      from { transform: translateY(20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    .modal-header {
      padding: 1.25rem;
      background: var(--gradient);
      color: white;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .modal-header h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
    }
    
    .close-btn {
      background: none;
      border: none;
      color: white;
      font-size: 1.5rem;
      cursor: pointer;
      padding: 0;
      line-height: 1;
      opacity: 0.8;
      transition: opacity 0.2s;
    }
    
    .close-btn:hover {
      opacity: 1;
    }
    
    .modal-body {
      padding: 1.5rem;
    }
    
    .modal-body p {
      margin-top: 0;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }
    
    .time-input {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
      background: rgba(99, 102, 241, 0.05);
      border-radius: var(--border-radius);
      padding: 0.5rem;
    }
    
    #limit-input {
      flex: 1;
      font-size: 1.5rem;
      padding: 0.5rem;
      border: none;
      background: transparent;
      color: var(--text-primary);
      text-align: right;
      font-weight: 600;
      width: 100%;
      -moz-appearance: textfield;
    }
    
    #limit-input::-webkit-outer-spin-button,
    #limit-input::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    
    .unit {
      padding: 0.5rem;
      color: var(--text-secondary);
      font-size: 0.875rem;
    }
    
    .time-presets {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 0.5rem;
      margin-bottom: 1rem;
    }
    
    .time-presets button {
      background: rgba(99, 102, 241, 0.1);
      border: none;
      border-radius: var(--border-radius);
      padding: 0.5rem;
      color: var(--primary-color);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .time-presets button:hover {
      background: rgba(99, 102, 241, 0.2);
    }
    
    .modal-footer {
      padding: 1rem 1.5rem;
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;
      border-top: 1px solid rgba(99, 102, 241, 0.1);
    }
    
    .modal-footer .btn {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }
  `;
  document.head.appendChild(style);
  
  // Add event listeners to modal
  const closeBtn = modal.querySelector('.close-btn');
  const cancelBtn = modal.querySelector('#cancel-limit');
  const saveBtn = modal.querySelector('#save-limit');
  const limitInput = modal.querySelector('#limit-input');
  const presetButtons = modal.querySelectorAll('.time-presets button');
  
  closeBtn.addEventListener('click', () => hideModal(modal));
  cancelBtn.addEventListener('click', () => hideModal(modal));
  
  presetButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      limitInput.value = btn.dataset.value;
    });
  });
  
  saveBtn.addEventListener('click', async () => {
    const limit = parseInt(limitInput.value);
    if (limit && limit > 0) {
      try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        const domain = getDomain(tab.url);
        const { sites = {} } = await chrome.storage.local.get(['sites']);
        
        sites[domain] = { ...sites[domain], timeSpent: sites[domain]?.timeSpent || 0, limit };
        
        await chrome.storage.local.set({ sites });
        updateLimitDisplay(limit);
        updateProgressBar(sites[domain].timeSpent, limit);
        
        hideModal(modal);
      } catch (error) {
        console.error('Failed to set limit:', error);
        showError('Failed to set time limit. Please try again.');
      }
    } else {
      limitInput.classList.add('error');
      setTimeout(() => limitInput.classList.remove('error'), 500);
    }
  });
}

// Show modal
function showModal(domain) {
  const modal = document.getElementById('time-limit-modal');
  if (!modal) return;
  
  const domainElement = modal.querySelector('#modal-domain');
  if (domainElement) {
    domainElement.textContent = domain;
  }
  
  modal.setAttribute('aria-hidden', 'false');
}

// Hide modal
function hideModal(modal) {
  modal.setAttribute('aria-hidden', 'true');
}

// Initialize Web Audio API
function initializeAudio() {
  try {
    // Create audio context
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
    
    // Initialize sound generators
    sounds.rain = createRainSound;
    sounds.forest = createForestSound;
    sounds.waves = createWavesSound;
  } catch (error) {
    console.error('Web Audio API not supported:', error);
  }
}

// Setup event listeners
function setupEventListeners() {
  // Focus Mode toggle
  elements.focusModeBtn.addEventListener('click', toggleFocusMode);
  
  // Set Limit button
  elements.setLimitBtn.addEventListener('click', handleSetLimit);
  
  // View Stats button
  elements.viewStatsBtn.addEventListener('click', openStatsPage);
  
  // Pomodoro Timer
  elements.startPomodoroBtn.addEventListener('click', togglePomodoro);
  
  // Sound buttons
  elements.soundButtons.forEach(button => {
    button.addEventListener('click', () => toggleSound(button.dataset.sound, button));
  });
}

// Focus Mode toggle
function toggleFocusMode() {
  elements.focusModePanel.classList.toggle('hidden');
  const isHidden = elements.focusModePanel.classList.contains('hidden');
  elements.focusModePanel.setAttribute('aria-hidden', isHidden);
  elements.focusModeBtn.setAttribute('aria-expanded', !isHidden);
}

// Time display functions
function updateTimeDisplay(timeInMs) {
  const { hours, minutes, seconds } = msToTime(timeInMs);
  elements.timeCounter.textContent = formatTime(hours, minutes, seconds);
}

function updateLimitDisplay(limitInMinutes) {
  elements.timeLimitDisplay.textContent = limitInMinutes ? `${limitInMinutes} min` : 'Not Set';
}

function updateProgressBar(timeSpent, limit) {
  const progressBar = elements.timeProgress.parentElement;
  if (limit) {
    const progress = Math.min((timeSpent / (limit * 60 * 1000)) * 100, 100);
    elements.timeProgress.style.width = `${progress}%`;
    progressBar.setAttribute('aria-valuenow', Math.round(progress));
  } else {
    elements.timeProgress.style.width = '0%';
    progressBar.setAttribute('aria-valuenow', 0);
  }
}

// Time Limit handlers
async function handleSetLimit() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    const domain = getDomain(tab.url);
    showModal(domain);
  } catch (error) {
    console.error('Failed to prepare limit setting:', error);
    showError('Failed to set time limit. Please try again.');
  }
}

// Pomodoro Timer handlers
function togglePomodoro() {
  const { pomodoro } = state;
  if (pomodoro.isActive) {
    clearInterval(pomodoro.interval);
    resetPomodoro();
  } else {
    startPomodoro();
  }
  pomodoro.isActive = !pomodoro.isActive;
  updatePomodoroButton();
}

function startPomodoro() {
  state.pomodoro.interval = setInterval(updatePomodoro, 1000);
}

function updatePomodoro() {
  const { pomodoro } = state;
  if (pomodoro.seconds === 0) {
    if (pomodoro.minutes === 0) {
      handlePomodoroComplete();
      return;
    }
    pomodoro.minutes--;
    pomodoro.seconds = 59;
  } else {
    pomodoro.seconds--;
  }
  updatePomodoroDisplay();
}

function handlePomodoroComplete() {
  const { pomodoro } = state;
  clearInterval(pomodoro.interval);
  pomodoro.breakMode = !pomodoro.breakMode;
  
  if (pomodoro.breakMode) {
    pomodoro.minutes = 5; // Break time
    playNotificationSound();
    showNotification('Time for a break!', 'Take 5 minutes to relax.');
  } else {
    pomodoro.minutes = 25; // Work time
    playNotificationSound();
    showNotification('Break complete!', 'Ready to focus again?');
  }
  
  pomodoro.seconds = 0;
  updatePomodoroDisplay();
  startPomodoro();
}

function resetPomodoro() {
  const { pomodoro } = state;
  pomodoro.minutes = 25;
  pomodoro.seconds = 0;
  pomodoro.breakMode = false;
  updatePomodoroDisplay();
}

function updatePomodoroDisplay() {
  const { minutes, seconds } = state.pomodoro;
  elements.pomodoroTimer.textContent = formatTime(0, minutes, seconds);
}

function updatePomodoroButton() {
  const btn = elements.startPomodoroBtn;
  const isActive = state.pomodoro.isActive;
  const icon = isActive ? 
    '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="6" y="4" width="4" height="16"></rect><rect x="14" y="4" width="4" height="16"></rect></svg>' :
    '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>';
  
  btn.innerHTML = `${icon} ${isActive ? 'Stop' : 'Start'} Pomodoro`;
  
  if (isActive) {
    btn.classList.add('active');
  } else {
    btn.classList.remove('active');
  }
}

// Sound generation functions
function createRainSound() {
  // Create noise for rain sound
  const bufferSize = 2 * audioContext.sampleRate;
  const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
  const output = noiseBuffer.getChannelData(0);
  
  for (let i = 0; i < bufferSize; i++) {
    output[i] = Math.random() * 2 - 1;
  }
  
  const noise = audioContext.createBufferSource();
  noise.buffer = noiseBuffer;
  noise.loop = true;
  
  // Create filter for rain sound
  const filter = audioContext.createBiquadFilter();
  filter.type = "highpass";
  filter.frequency.value = 1000;
  
  // Create gain node to control volume
  const gainNode = audioContext.createGain();
  gainNode.gain.value = 0.15;
  
  // Connect nodes
  noise.connect(filter);
  filter.connect(gainNode);
  gainNode.connect(audioContext.destination);
  
  // Start playing
  noise.start(0);
  
  // Return nodes for later stopping
  return { sources: [noise], gainNode };
}

function createForestSound() {
  // Create oscillators for bird chirps
  const birdSounds = [];
  
  // Create base ambient noise
  const bufferSize = 2 * audioContext.sampleRate;
  const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
  const output = noiseBuffer.getChannelData(0);
  
  for (let i = 0; i < bufferSize; i++) {
    output[i] = Math.random() * 0.2 - 0.1;
  }
  
  const noise = audioContext.createBufferSource();
  noise.buffer = noiseBuffer;
  noise.loop = true;
  
  // Create filter for ambient noise
  const filter = audioContext.createBiquadFilter();
  filter.type = "lowpass";
  filter.frequency.value = 800;
  
  // Create gain node for overall volume
  const gainNode = audioContext.createGain();
  gainNode.gain.value = 0.2;
  
  // Connect base noise
  noise.connect(filter);
  filter.connect(gainNode);
  gainNode.connect(audioContext.destination);
  
  // Start playing
  noise.start(0);
  
  // Create bird chirp sounds that play occasionally
  function createBirdChirp() {
    if (!currentSound) return; // Stop if sound is turned off
    
    const osc = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    // Random bird sound parameters
    const freq = 1000 + Math.random() * 1000;
    const duration = 0.1 + Math.random() * 0.2;
    
    osc.frequency.value = freq;
    osc.type = "sine";
    
    gainNode.gain.value = 0;
    
    // Connect
    osc.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // Create chirp envelope
    const now = audioContext.currentTime;
    gainNode.gain.setValueAtTime(0, now);
    gainNode.gain.linearRampToValueAtTime(0.1 + Math.random() * 0.1, now + 0.05);
    gainNode.gain.linearRampToValueAtTime(0, now + duration);
    
    // Start and stop
    osc.start(now);
    osc.stop(now + duration);
    
    // Schedule next chirp
    if (currentSound) {
      setTimeout(createBirdChirp, 2000 + Math.random() * 5000);
    }
  }
  
  // Start bird chirps
  setTimeout(createBirdChirp, 1000);
  
  return { sources: [noise], gainNode };
}

function createWavesSound() {
  // Create noise for wave sound
  const bufferSize = 2 * audioContext.sampleRate;
  const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
  const output = noiseBuffer.getChannelData(0);
  
  for (let i = 0; i < bufferSize; i++) {
    output[i] = Math.random() * 2 - 1;
  }
  
  const noise = audioContext.createBufferSource();
  noise.buffer = noiseBuffer;
  noise.loop = true;
  
  // Create filter for wave sound
  const filter = audioContext.createBiquadFilter();
  filter.type = "lowpass";
  filter.frequency.value = 400;
  
  // Create gain node for wave sound
  const gainNode = audioContext.createGain();
  gainNode.gain.value = 0.1;
  
  // Create LFO for wave effect
  const lfo = audioContext.createOscillator();
  const lfoGain = audioContext.createGain();
  
  lfo.frequency.value = 0.1;
  lfoGain.gain.value = 0.1;
  
  lfo.connect(lfoGain);
  lfoGain.connect(gainNode.gain);
  
  // Connect nodes
  noise.connect(filter);
  filter.connect(gainNode);
  gainNode.connect(audioContext.destination);
  
  // Start playing
  noise.start(0);
  lfo.start(0);
  
  // Return nodes for later stopping
  return { sources: [noise, lfo], gainNode };
}

// Play notification sound
function playNotificationSound() {
  try {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.type = 'sine';
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    const now = audioContext.currentTime;
    
    // Play a simple "ding" sound
    oscillator.frequency.setValueAtTime(880, now);
    oscillator.frequency.setValueAtTime(1320, now + 0.1);
    
    gainNode.gain.setValueAtTime(0, now);
    gainNode.gain.linearRampToValueAtTime(0.3, now + 0.01);
    gainNode.gain.linearRampToValueAtTime(0, now + 0.3);
    
    oscillator.start(now);
    oscillator.stop(now + 0.3);
  } catch (error) {
    console.error('Failed to play notification sound:', error);
  }
}

// Ambient Sound handlers
function toggleSound(soundType, button) {
  // If we're already playing this sound, stop it
  if (currentSound && button.classList.contains('active')) {
    stopCurrentSound();
    return;
  }
  
  // Stop any currently playing sound
  if (currentSound) {
    stopCurrentSound();
  }
  
  // Remove active class from all buttons
  elements.soundButtons.forEach(btn => btn.classList.remove('active'));
  
  // Start the new sound
  try {
    // Make sure audio context is running
    if (audioContext.state === 'suspended') {
      audioContext.resume();
    }
    
    // Create and play the selected sound
    currentSound = sounds[soundType]();
    button.classList.add('active');
  } catch (error) {
    console.error('Failed to play sound:', error);
    showError('Failed to play sound. Please try again.');
  }
}

function stopCurrentSound() {
  if (!currentSound) return;
  
  try {
    // Stop all sound sources
    if (currentSound.sources) {
      currentSound.sources.forEach(source => {
        try {
          source.stop();
        } catch (e) {
          // Ignore errors if source is already stopped
        }
      });
    }
    
    // Fade out to avoid clicks
    if (currentSound.gainNode) {
      const now = audioContext.currentTime;
      currentSound.gainNode.gain.linearRampToValueAtTime(0, now + 0.1);
    }
    
    // Remove active class from all buttons
    elements.soundButtons.forEach(btn => btn.classList.remove('active'));
    
    // Clear current sound
    currentSound = null;
  } catch (error) {
    console.error('Error stopping sound:', error);
  }
}

// Motivational quotes
const quotes = [
  "Focus on being productive instead of busy.",
  "The key to success is to focus on goals, not obstacles.",
  "Do one thing at a time, and do it well.",
  "Stay focused, go after your dreams, and keep moving toward your goals.",
  "Where focus goes, energy flows.",
  "Concentrate all your thoughts upon the work at hand.",
  "Focus is the key to accomplishing what is necessary.",
  "The successful warrior is the average person, with laser-like focus."
];

function updateMotivationalQuote() {
  const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
  elements.motivationQuote.textContent = `"${randomQuote}"`;
}

// Utility functions
function getDomain(url) {
  if (!url) return '-';
  try {
    return new URL(url).hostname;
  } catch {
    return '-';
  }
}

function msToTime(ms) {
  const hours = Math.floor(ms / (1000 * 60 * 60));
  const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((ms % (1000 * 60)) / 1000);
  return { hours, minutes, seconds };
}

function formatTime(hours, minutes, seconds) {
  return [hours, minutes, seconds].map(num => num.toString().padStart(2, '0')).join(':');
}

function showNotification(title, message) {
  // Use chrome.runtime.getURL to get the absolute URL for the icon
  const iconUrl = chrome.runtime.getURL('assets/icons/icon128.png');
  
  if (chrome.notifications) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: iconUrl,
      title: title,
      message: message,
      priority: 1,
      requireInteraction: true
    }, (notificationId) => {
      if (chrome.runtime.lastError) {
        console.error('Chrome notification error:', chrome.runtime.lastError);
        // Fall back to Web Notifications API
        fallbackNotification(title, message, iconUrl);
      }
    });
  } else {
    // Fall back to Web Notifications API
    fallbackNotification(title, message, iconUrl);
  }
}

function fallbackNotification(title, message, iconUrl) {
  if (Notification && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      body: message,
      icon: iconUrl
    });
    
    notification.onclick = function() {
      window.focus();
      notification.close();
    };
  } else if (Notification && Notification.permission !== 'denied') {
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        fallbackNotification(title, message, iconUrl);
      }
    });
  }
}

function showError(message) {
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.innerHTML = `
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" y1="8" x2="12" y2="12"></line>
      <line x1="12" y1="16" x2="12.01" y2="16"></line>
    </svg>
    ${message}
  `;
  document.body.appendChild(errorDiv);
  
  setTimeout(() => {
    errorDiv.remove();
  }, 3000);
}

// Open statistics page
function openStatsPage() {
  chrome.tabs.create({ url: chrome.runtime.getURL('stats.html') });
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePopup);