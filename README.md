# Auto Page Timer with Focus Mode

A Chrome extension that helps users track their time spent on websites and maintain focus with a Pomodoro timer and ambient sounds.

## Features

- **Website Time Tracking**: Monitors how much time is spent on each website
- **Custom Time Limit Alerts**: Set time limits per domain and receive notifications when limits are reached
- **Focus Recovery Mode**: Pomodoro timer with ambient sounds to help regain focus
- **Daily Usage Stats**: View your website usage patterns
- **Customizable Settings**: Configure notifications, sound alerts, and default behaviors

## UI Design

The extension features a modern, professional UI with:

- **Clean Interface**: Minimalist design that's easy to navigate
- **Dark Mode Support**: Automatically adapts to your system preferences
- **Visual Hierarchy**: Important information stands out with gradient highlights
- **Responsive Components**: Interactive elements with subtle animations
- **Accessibility Features**: Proper ARIA attributes and keyboard navigation

## Technical Features

- **Web Audio API**: Procedurally generated ambient sounds (rain, forest, waves) without requiring external audio files
- **Chrome Storage API**: Local storage for all user data and preferences
- **Chrome Tabs API**: Accurate tracking of active tabs and time spent
- **Responsive Design**: Works across different screen sizes and adapts to system preferences
- **Modular Architecture**: Clean separation of concerns for easier maintenance

## Installation

### From Chrome Web Store (Coming Soon)

1. Visit the Chrome Web Store page for Auto Page Timer
2. Click "Add to Chrome"
3. Confirm the installation

### Manual Installation (Developer Mode)

1. Download or clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top-right corner
4. Click "Load unpacked" and select the extension directory
5. The extension should now appear in your toolbar

## Usage

### Setting Up Website Limits

1. Click on the extension icon in your toolbar
2. Click "Set Limit" to set a limit for the current site
3. Or go to the Options page to manage all your tracked sites

### Using Focus Mode

1. When you reach a time limit (or manually), click "Focus Mode"
2. Use the Pomodoro timer to work in focused intervals
3. Choose ambient sounds to help maintain concentration

### Customizing Settings

1. Right-click the extension icon and select "Options"
2. Configure notification preferences, sound alerts, and more
3. Add or remove websites from tracking

## Privacy

This extension stores all data locally on your device. No data is sent to external servers.

## License

MIT License 