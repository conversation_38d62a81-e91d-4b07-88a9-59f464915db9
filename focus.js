// DOM Elements
const timerDisplay = document.getElementById('timer');
const progressBar = document.getElementById('progress');
const statusText = document.getElementById('status');
const startButton = document.getElementById('start');
const pauseButton = document.getElementById('pause');
const resetButton = document.getElementById('reset');
const soundButtons = document.querySelectorAll('.sound-btn');
const quoteElement = document.getElementById('quote');

// Timer variables
let timeLeft = 25 * 60; // 25 minutes in seconds
let timerId = null;
let isBreak = false;

// Audio Context for sound generation
let audioContext;
let currentSound = null;

// Sound generators
const sounds = {
  rain: null,
  forest: null,
  waves: null
};

// Motivational quotes
const quotes = [
  "Focus on being productive instead of busy.",
  "The key to success is to focus on goals, not obstacles.",
  "Do one thing at a time, and do it well.",
  "Stay focused, go after your dreams, and keep moving toward your goals.",
  "Where focus goes, energy flows.",
  "Concentrate all your thoughts upon the work at hand.",
  "Focus is the key to accomplishing what is necessary.",
  "The successful warrior is the average person, with laser-like focus."
];

// Initialize Web Audio API
function initializeAudio() {
  try {
    // Create audio context
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
    
    // Initialize sound generators
    sounds.rain = createRainSound;
    sounds.forest = createForestSound;
    sounds.waves = createWavesSound;
  } catch (error) {
    console.error('Web Audio API not supported:', error);
  }
}

// Timer functions
function startTimer() {
  if (!timerId) {
    timerId = setInterval(updateTimer, 1000);
    startButton.disabled = true;
    pauseButton.disabled = false;
  }
}

function pauseTimer() {
  clearInterval(timerId);
  timerId = null;
  startButton.disabled = false;
  pauseButton.disabled = true;
}

function resetTimer() {
  clearInterval(timerId);
  timerId = null;
  isBreak = false;
  timeLeft = 25 * 60;
  updateDisplay();
  startButton.disabled = false;
  pauseButton.disabled = true;
  statusText.textContent = 'Focus Time';
}

function updateTimer() {
  timeLeft--;
  
  if (timeLeft <= 0) {
    clearInterval(timerId);
    timerId = null;
    
    // Play notification sound
    playNotificationSound();
    
    if (!isBreak) {
      // Switch to break time
      timeLeft = 5 * 60; // 5 minutes break
      isBreak = true;
      statusText.textContent = 'Break Time';
      showNotification('Time for a break!', 'Take 5 minutes to relax and recharge.');
    } else {
      // Switch back to focus time
      timeLeft = 25 * 60;
      isBreak = false;
      statusText.textContent = 'Focus Time';
      showNotification('Break finished!', 'Time to get back to work.');
    }
    
    startButton.disabled = false;
  }
  
  updateDisplay();
}

function updateDisplay() {
  const minutes = Math.floor(timeLeft / 60);
  const seconds = timeLeft % 60;
  timerDisplay.textContent = `${padNumber(minutes)}:${padNumber(seconds)}`;
  
  // Update progress bar
  const totalTime = isBreak ? 5 * 60 : 25 * 60;
  const progress = ((totalTime - timeLeft) / totalTime) * 100;
  progressBar.style.width = `${progress}%`;
}

function padNumber(num) {
  return num.toString().padStart(2, '0');
}

// Sound generation functions
function createRainSound() {
  // Create noise for rain sound
  const bufferSize = 2 * audioContext.sampleRate;
  const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
  const output = noiseBuffer.getChannelData(0);
  
  for (let i = 0; i < bufferSize; i++) {
    output[i] = Math.random() * 2 - 1;
  }
  
  const noise = audioContext.createBufferSource();
  noise.buffer = noiseBuffer;
  noise.loop = true;
  
  // Create filter for rain sound
  const filter = audioContext.createBiquadFilter();
  filter.type = "highpass";
  filter.frequency.value = 1000;
  
  // Create gain node to control volume
  const gainNode = audioContext.createGain();
  gainNode.gain.value = 0.15;
  
  // Connect nodes
  noise.connect(filter);
  filter.connect(gainNode);
  gainNode.connect(audioContext.destination);
  
  // Start playing
  noise.start(0);
  
  // Return nodes for later stopping
  return { sources: [noise], gainNode };
}

function createForestSound() {
  // Create oscillators for bird chirps
  const birdSounds = [];
  
  // Create base ambient noise
  const bufferSize = 2 * audioContext.sampleRate;
  const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
  const output = noiseBuffer.getChannelData(0);
  
  for (let i = 0; i < bufferSize; i++) {
    output[i] = Math.random() * 0.2 - 0.1;
  }
  
  const noise = audioContext.createBufferSource();
  noise.buffer = noiseBuffer;
  noise.loop = true;
  
  // Create filter for ambient noise
  const filter = audioContext.createBiquadFilter();
  filter.type = "lowpass";
  filter.frequency.value = 800;
  
  // Create gain node for overall volume
  const gainNode = audioContext.createGain();
  gainNode.gain.value = 0.2;
  
  // Connect base noise
  noise.connect(filter);
  filter.connect(gainNode);
  gainNode.connect(audioContext.destination);
  
  // Start playing
  noise.start(0);
  
  // Create bird chirp sounds that play occasionally
  function createBirdChirp() {
    if (!currentSound) return; // Stop if sound is turned off
    
    const osc = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    // Random bird sound parameters
    const freq = 1000 + Math.random() * 1000;
    const duration = 0.1 + Math.random() * 0.2;
    
    osc.frequency.value = freq;
    osc.type = "sine";
    
    gainNode.gain.value = 0;
    
    // Connect
    osc.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    // Create chirp envelope
    const now = audioContext.currentTime;
    gainNode.gain.setValueAtTime(0, now);
    gainNode.gain.linearRampToValueAtTime(0.1 + Math.random() * 0.1, now + 0.05);
    gainNode.gain.linearRampToValueAtTime(0, now + duration);
    
    // Start and stop
    osc.start(now);
    osc.stop(now + duration);
    
    // Schedule next chirp
    if (currentSound) {
      setTimeout(createBirdChirp, 2000 + Math.random() * 5000);
    }
  }
  
  // Start bird chirps
  setTimeout(createBirdChirp, 1000);
  
  return { sources: [noise], gainNode };
}

function createWavesSound() {
  // Create noise for wave sound
  const bufferSize = 2 * audioContext.sampleRate;
  const noiseBuffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
  const output = noiseBuffer.getChannelData(0);
  
  for (let i = 0; i < bufferSize; i++) {
    output[i] = Math.random() * 2 - 1;
  }
  
  const noise = audioContext.createBufferSource();
  noise.buffer = noiseBuffer;
  noise.loop = true;
  
  // Create filter for wave sound
  const filter = audioContext.createBiquadFilter();
  filter.type = "lowpass";
  filter.frequency.value = 400;
  
  // Create gain node for wave sound
  const gainNode = audioContext.createGain();
  gainNode.gain.value = 0.1;
  
  // Create LFO for wave effect
  const lfo = audioContext.createOscillator();
  const lfoGain = audioContext.createGain();
  
  lfo.frequency.value = 0.1;
  lfoGain.gain.value = 0.1;
  
  lfo.connect(lfoGain);
  lfoGain.connect(gainNode.gain);
  
  // Connect nodes
  noise.connect(filter);
  filter.connect(gainNode);
  gainNode.connect(audioContext.destination);
  
  // Start playing
  noise.start(0);
  lfo.start(0);
  
  // Return nodes for later stopping
  return { sources: [noise, lfo], gainNode };
}

// Play notification sound
function playNotificationSound() {
  try {
    if (!audioContext) {
      initializeAudio();
    }
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.type = 'sine';
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    const now = audioContext.currentTime;
    
    // Play a simple "ding" sound
    oscillator.frequency.setValueAtTime(880, now);
    oscillator.frequency.setValueAtTime(1320, now + 0.1);
    
    gainNode.gain.setValueAtTime(0, now);
    gainNode.gain.linearRampToValueAtTime(0.3, now + 0.01);
    gainNode.gain.linearRampToValueAtTime(0, now + 0.3);
    
    oscillator.start(now);
    oscillator.stop(now + 0.3);
  } catch (error) {
    console.error('Failed to play notification sound:', error);
  }
}

// Sound control functions
function toggleSound(soundType, button) {
  // If we're already playing this sound, stop it
  if (currentSound && button.classList.contains('active')) {
    stopCurrentSound();
    return;
  }
  
  // Stop any currently playing sound
  if (currentSound) {
    stopCurrentSound();
  }
  
  // Remove active class from all buttons
  soundButtons.forEach(btn => btn.classList.remove('active'));
  
  // Start the new sound
  try {
    // Make sure audio context is initialized
    if (!audioContext) {
      initializeAudio();
    }
    
    // Make sure audio context is running
    if (audioContext.state === 'suspended') {
      audioContext.resume();
    }
    
    // Create and play the selected sound
    currentSound = sounds[soundType]();
    button.classList.add('active');
  } catch (error) {
    console.error('Failed to play sound:', error);
    showNotification('Error', 'Failed to play sound. Please try again.');
  }
}

function stopCurrentSound() {
  if (!currentSound) return;
  
  try {
    // Stop all sound sources
    if (currentSound.sources) {
      currentSound.sources.forEach(source => {
        try {
          source.stop();
        } catch (e) {
          // Ignore errors if source is already stopped
        }
      });
    }
    
    // Fade out to avoid clicks
    if (currentSound.gainNode) {
      const now = audioContext.currentTime;
      currentSound.gainNode.gain.linearRampToValueAtTime(0, now + 0.1);
    }
    
    // Remove active class from all buttons
    soundButtons.forEach(btn => btn.classList.remove('active'));
    
    // Clear current sound
    currentSound = null;
  } catch (error) {
    console.error('Error stopping sound:', error);
  }
}

// Show browser notification
function showNotification(title, message) {
  // Get icon URL using chrome.runtime.getURL
  const iconUrl = chrome.runtime.getURL('assets/icons/icon48.png');
  
  // First try using Chrome notifications API
  if (chrome.notifications) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: iconUrl,
      title: title,
      message: message,
      priority: 2,
      requireInteraction: true
    }, (notificationId) => {
      if (chrome.runtime.lastError) {
        console.error('Chrome notification error:', chrome.runtime.lastError);
        // Fall back to Web Notifications API
        fallbackNotification(title, message, iconUrl);
      }
    });
  } else {
    // Fall back to Web Notifications API
    fallbackNotification(title, message, iconUrl);
  }
}

// Web Notifications fallback
function fallbackNotification(title, message, iconUrl) {
  // Check if Notifications API is supported and permission is granted
  if (Notification && Notification.permission === 'granted') {
    const notification = new Notification(title, {
      body: message,
      icon: iconUrl
    });
    
    notification.onclick = function() {
      window.focus();
      notification.close();
    };
  } else if (Notification && Notification.permission !== 'denied') {
    // Request permission
    Notification.requestPermission().then(permission => {
      if (permission === 'granted') {
        fallbackNotification(title, message, iconUrl);
      }
    });
  }
}

// Update quote randomly
function updateQuote() {
  const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
  quoteElement.textContent = `"${randomQuote}"`;
}

// Event listeners
startButton.addEventListener('click', startTimer);
pauseButton.addEventListener('click', pauseTimer);
resetButton.addEventListener('click', resetTimer);

soundButtons.forEach(button => {
  button.addEventListener('click', () => {
    const soundType = button.dataset.sound;
    toggleSound(soundType, button);
  });
});

// Initialize
document.addEventListener('DOMContentLoaded', () => {
  updateDisplay();
  updateQuote();
  pauseButton.disabled = true;
  
  // Initialize audio
  initializeAudio();
  
  // Request notification permission
  if (Notification.permission !== 'granted' && Notification.permission !== 'denied') {
    Notification.requestPermission();
  }
});

// Update quote every 5 minutes
setInterval(updateQuote, 5 * 60 * 1000);