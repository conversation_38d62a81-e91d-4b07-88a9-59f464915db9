// DOM Elements
const totalTimeElement = document.getElementById('total-time');
const sitesVisitedElement = document.getElementById('sites-visited');
const sitesOverLimitElement = document.getElementById('sites-over-limit');
const dateRangeElement = document.getElementById('date-range');
const siteListElement = document.getElementById('site-list');
const tipsListElement = document.getElementById('tips-list');
const periodButtons = document.querySelectorAll('.date-selector button');
const timeChart = document.getElementById('time-chart');

// State
let currentPeriod = 'today';
let chartInstance = null;

// Initialize
document.addEventListener('DOMContentLoaded', () => {
  // Set up period buttons
  periodButtons.forEach(button => {
    button.addEventListener('click', () => {
      periodButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');
      currentPeriod = button.dataset.period;
      dateRangeElement.textContent = getPeriodLabel(currentPeriod);
      loadStats(currentPeriod);
    });
  });

  // Load initial stats
  loadStats(currentPeriod);
});

// Load statistics based on selected period
async function loadStats(period) {
  try {
    // Show loading state
    document.body.classList.add('loading');
    
    const { sites = {} } = await chrome.storage.local.get(['sites']);
    
    // Filter sites based on the selected period
    const filteredSites = await filterSitesByPeriod(sites, period);
    
    // Update summary stats
    updateSummaryStats(filteredSites);
    
    // Update site list
    updateSiteList(filteredSites);
    
    // Update chart
    updateChart(filteredSites);
    
    // Update tips
    updateTips(filteredSites);
    
    // Remove loading state
    document.body.classList.remove('loading');
  } catch (error) {
    console.error('Failed to load stats:', error);
    showError('Failed to load statistics. Please try again.');
    
    // Show fallback UI
    document.getElementById('total-time').textContent = '0h 0m';
    document.getElementById('sites-visited').textContent = '0';
    document.getElementById('sites-over-limit').textContent = '0';
    
    const siteList = document.getElementById('site-list');
    if (siteList) {
      siteList.innerHTML = '<div class="no-data">No data available. Please try again later.</div>';
    }
    
    const chartContainer = document.querySelector('.chart-container');
    if (chartContainer) {
      chartContainer.innerHTML = '<div class="no-data">Failed to load chart data.</div>';
    }
    
    // Remove loading state
    document.body.classList.remove('loading');
  }
}

// Filter sites based on time period
async function filterSitesByPeriod(sites, period) {
  try {
    const { history = { daily: {}, weekly: {}, monthly: {} } } = await chrome.storage.local.get(['history']);
    const now = new Date();
    const today = formatDate(now);
    const currentWeek = getWeekNumber(now);
    const currentMonth = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
    
    let filteredData = {};
    
    switch (period) {
      case 'today':
        // Use today's data from history, or current data if not available
        filteredData = history.daily[today] || sites;
        break;
        
      case 'week':
        // Combine all data from this week
        filteredData = history.weekly[currentWeek] || {};
        break;
        
      case 'month':
        // Combine all data from this month
        filteredData = history.monthly[currentMonth] || {};
        break;
        
      default:
        filteredData = sites;
    }
    
    // Convert the historical data format to match the current sites format
    const result = {};
    for (const domain in filteredData) {
      const timeSpent = typeof filteredData[domain] === 'number' 
        ? filteredData[domain] 
        : filteredData[domain].timeSpent || 0;
        
      result[domain] = {
        timeSpent: timeSpent,
        limit: sites[domain]?.limit || null
      };
    }
    
    return result;
  } catch (error) {
    console.error('Error filtering sites by period:', error);
    return sites;
  }
}

// Format date as YYYY-MM-DD
function formatDate(date) {
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

// Get ISO week number (YYYY-WW)
function getWeekNumber(date) {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay() || 7));
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  const weekNo = Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  return `${d.getUTCFullYear()}-${weekNo.toString().padStart(2, '0')}`;
}

// Update summary statistics
function updateSummaryStats(sites) {
  let totalTime = 0;
  let sitesVisited = Object.keys(sites).length;
  let sitesOverLimit = 0;
  
  // Calculate stats
  Object.values(sites).forEach(site => {
    totalTime += site.timeSpent || 0;
    
    if (site.limit && site.timeSpent > site.limit * 60 * 1000) {
      sitesOverLimit++;
    }
  });
  
  // Update UI
  totalTimeElement.textContent = formatTimeForDisplay(totalTime);
  sitesVisitedElement.textContent = sitesVisited;
  sitesOverLimitElement.textContent = sitesOverLimit;
}

// Update site list
function updateSiteList(sites) {
  // Clear current list
  siteListElement.innerHTML = '';
  
  // Check if there are sites to display
  if (Object.keys(sites).length === 0) {
    const noDataElement = document.createElement('div');
    noDataElement.className = 'no-data';
    noDataElement.textContent = 'No data available for the selected period';
    siteListElement.appendChild(noDataElement);
    return;
  }
  
  // Sort sites by time spent (descending)
  const sortedSites = Object.entries(sites)
    .sort(([, a], [, b]) => (b.timeSpent || 0) - (a.timeSpent || 0));
  
  // Create site items
  sortedSites.forEach(([domain, data]) => {
    const siteItem = document.createElement('div');
    siteItem.className = 'site-item';
    
    // Determine status
    let status = 'within';
    let statusText = 'Within Limit';
    
    if (data.limit) {
      const percentUsed = (data.timeSpent || 0) / (data.limit * 60 * 1000);
      
      if (percentUsed > 1) {
        status = 'over';
        statusText = 'Over Limit';
      } else if (percentUsed > 0.8) {
        status = 'close';
        statusText = 'Near Limit';
      }
    } else {
      status = '';
      statusText = 'No Limit Set';
    }
    
    // Create site item HTML
    siteItem.innerHTML = `
      <div class="site-domain">${domain}</div>
      <div class="site-time">${formatTimeForDisplay(data.timeSpent || 0)}</div>
      <div class="site-status ${status ? 'status-' + status : ''}">
        ${status ? `<span class="status-indicator"></span>` : ''}
        ${statusText}
      </div>
    `;
    
    siteListElement.appendChild(siteItem);
  });
}

// Update chart
function updateChart(sites) {
  try {
    // Check if Chart is defined
    if (typeof Chart === 'undefined') {
      console.error('Chart.js is not loaded');
      const chartContainer = document.querySelector('.chart-container');
      if (chartContainer) {
        chartContainer.innerHTML = '<div class="no-data">Chart library failed to load. Please reload the page.</div>';
      }
      return;
    }
    
    // Destroy existing chart if it exists
    if (chartInstance) {
      chartInstance.destroy();
    }
    
    // Prepare data for chart
    const sortedSites = Object.entries(sites)
      .sort(([, a], [, b]) => (b.timeSpent || 0) - (a.timeSpent || 0))
      .slice(0, 5); // Top 5 sites
    
    // If no data, show message
    if (sortedSites.length === 0) {
      const chartContainer = document.querySelector('.chart-container');
      if (chartContainer) {
        chartContainer.innerHTML = '<div class="no-data">No data available for the selected period</div>';
      }
      return;
    }
    
    const labels = sortedSites.map(([domain]) => domain);
    const data = sortedSites.map(([, site]) => Math.round((site.timeSpent || 0) / (60 * 1000))); // Convert to minutes
    
    // Create gradient for bars
    const ctx = timeChart.getContext('2d');
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, '#6366f1');
    gradient.addColorStop(1, '#8b5cf6');
    
    // Create chart
    chartInstance = new Chart(ctx, {
      type: 'bar',
      data: {
        labels,
        datasets: [{
          label: 'Minutes Spent',
          data,
          backgroundColor: gradient,
          borderRadius: 6,
          maxBarThickness: 40
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return `${context.parsed.y} minutes`;
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Minutes'
            },
            grid: {
              display: true,
              color: 'rgba(0, 0, 0, 0.05)'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  } catch (error) {
    console.error('Error creating chart:', error);
    const chartContainer = document.querySelector('.chart-container');
    if (chartContainer) {
      chartContainer.innerHTML = '<div class="no-data">Failed to create chart. Please reload the page.</div>';
    }
  }
}

// Update tips based on browsing habits
function updateTips(sites) {
  // In a real implementation, you would analyze the data and provide personalized tips
  // For now, we'll just show generic tips
  
  // Check if there are any sites over limit
  const hasOverLimit = Object.values(sites).some(site => 
    site.limit && site.timeSpent > site.limit * 60 * 1000
  );
  
  // Get top time-consuming site
  let topSite = null;
  let maxTime = 0;
  
  Object.entries(sites).forEach(([domain, data]) => {
    if ((data.timeSpent || 0) > maxTime) {
      maxTime = data.timeSpent || 0;
      topSite = domain;
    }
  });
  
  // Generate tips
  const tips = [];
  
  if (hasOverLimit) {
    tips.push('Consider increasing your time limits or setting more realistic goals.');
    tips.push('Try using the Focus Recovery Mode when you reach your time limit.');
  }
  
  if (topSite) {
    tips.push(`You spend a lot of time on ${topSite}. Try scheduling specific times for this site.`);
  }
  
  if (Object.keys(sites).length > 5) {
    tips.push('You visit many different sites. Try to focus on fewer sites to improve productivity.');
  }
  
  // Add default tips if we don't have enough
  const defaultTips = [
    'Try using the Pomodoro technique (25 min work, 5 min break) for better focus.',
    'Consider setting stricter time limits for entertainment sites.',
    'Use ambient sounds in Focus Mode to block distractions.',
    'Take regular breaks to maintain productivity throughout the day.',
    'Try to batch similar tasks together for better efficiency.'
  ];
  
  // Combine custom and default tips, ensuring we have at least 5
  const allTips = [...tips, ...defaultTips];
  const finalTips = allTips.slice(0, 5);
  
  // Update tips list
  tipsListElement.innerHTML = '';
  finalTips.forEach(tip => {
    const li = document.createElement('li');
    li.textContent = tip;
    tipsListElement.appendChild(li);
  });
}

// Helper functions
function formatTimeForDisplay(ms) {
  const hours = Math.floor(ms / (1000 * 60 * 60));
  const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

function getPeriodLabel(period) {
  switch (period) {
    case 'today':
      return 'Today';
    case 'week':
      return 'This Week';
    case 'month':
      return 'This Month';
    default:
      return 'Today';
  }
}

function showError(message) {
  console.error(message);
  
  // Create error notification
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.style.cssText = `
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #ef4444;
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    animation: slideUp 0.3s ease;
  `;
  
  errorDiv.textContent = message;
  document.body.appendChild(errorDiv);
  
  // Remove after 3 seconds
  setTimeout(() => {
    errorDiv.style.opacity = '0';
    setTimeout(() => errorDiv.remove(), 300);
  }, 3000);
} 