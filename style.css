/* Modern CSS Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* CSS Variables */
:root {
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --secondary-color: #64748b;
  --secondary-hover: #475569;
  --background: #f8fafc;
  --card-bg: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --success: #22c55e;
  --danger: #ef4444;
  --border-radius: 12px;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  --transition: all 0.3s ease;
  --gradient: linear-gradient(135deg, #6366f1, #8b5cf6);
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --card-bg: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5);
  }
}

/* Base Styles */
body {
  font-family: 'Inter', sans-serif;
  background: var(--background);
  color: var(--text-primary);
  line-height: 1.5;
}

.container {
  width: 360px;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

/* Header Styles */
.header {
  padding: 1.5rem;
  background: var(--gradient);
  color: white;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('assets/icons/icon128.png') no-repeat;
  background-position: right -20px top -20px;
  background-size: 100px;
  opacity: 0.1;
  z-index: 0;
}

.site-info {
  position: relative;
  z-index: 1;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
}

.site-info .label {
  font-size: 0.875rem;
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.domain {
  font-weight: 600;
  font-size: 1.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* Main Content */
.main-content {
  padding: 1.5rem;
}

/* Time Section Styles */
.time-section {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.time-display {
  margin-bottom: 1.25rem;
}

.counter-wrapper {
  text-align: center;
  margin-bottom: 1rem;
}

.counter {
  font-size: 3rem;
  font-weight: 700;
  background: var(--gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-variant-numeric: tabular-nums;
  letter-spacing: -1px;
}

.limit-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--text-secondary);
  font-size: 0.875rem;
  padding: 0 0.5rem;
}

/* Progress Bar Styles */
.progress-wrapper {
  height: 8px;
  background: rgba(99, 102, 241, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 0.75rem;
}

.progress-bar {
  height: 100%;
  background: var(--gradient);
  transition: width 0.5s ease;
  width: 0;
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

/* Button Styles */
.actions-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem 1.25rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9375rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.btn svg {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.2s;
}

.btn:hover svg {
  transform: scale(1.1);
}

.primary {
  background: var(--gradient);
  color: white;
}

.primary:hover {
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
  transform: translateY(-2px);
}

.secondary {
  background: rgba(100, 116, 139, 0.1);
  color: var(--text-primary);
  border: 1px solid rgba(100, 116, 139, 0.2);
}

.secondary:hover {
  background: rgba(100, 116, 139, 0.15);
  transform: translateY(-2px);
}

/* Focus Panel Styles */
.focus-panel {
  background: var(--card-bg);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border-top: 1px solid rgba(99, 102, 241, 0.1);
}

.focus-panel:not(.hidden) {
  transform: translateY(0);
  opacity: 1;
}

.hidden {
  display: none;
}

/* Pomodoro Section Styles */
.pomodoro-section {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.pomodoro-section h3 {
  font-size: 1.125rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.pomodoro-section h3::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--gradient);
  border-radius: 1px;
}

.timer-display {
  font-size: 3.5rem;
  font-weight: 700;
  background: var(--gradient);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin: 1.5rem 0;
  font-variant-numeric: tabular-nums;
}

/* Sound Button Styles */
.sounds-section {
  margin-bottom: 2rem;
}

.sounds-section h3 {
  font-size: 1.125rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.sounds-section h3::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--gradient);
  border-radius: 1px;
}

.sound-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
}

.sound-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.sound-btn:hover {
  background: rgba(99, 102, 241, 0.05);
  transform: translateY(-2px);
}

.sound-btn.active {
  background: rgba(99, 102, 241, 0.1);
  border-color: rgba(99, 102, 241, 0.3);
}

.sound-btn.active svg {
  color: var(--primary-color);
}

.motivation-section {
  text-align: center;
}

.quote {
  font-style: italic;
  color: var(--text-secondary);
  position: relative;
  padding: 1rem 1.5rem;
  line-height: 1.6;
}

.quote::before,
.quote::after {
  content: """;
  font-size: 2rem;
  color: rgba(99, 102, 241, 0.3);
  position: absolute;
  top: 0;
  left: 0;
}

.quote::after {
  content: """;
  left: auto;
  right: 0;
}

/* Modal Styles */
#limit-input.error {
  animation: shake 0.4s ease;
  box-shadow: 0 0 0 2px var(--danger);
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

.error-message {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger);
  padding: 0.75rem;
  border-radius: var(--border-radius);
  margin-top: 1rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideUp 0.3s forwards;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}