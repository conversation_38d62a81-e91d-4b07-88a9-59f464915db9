// Initialize storage with default values if not set
chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.local.get(['sites', 'settings', 'history'], (result) => {
    if (!result.sites) {
      chrome.storage.local.set({
        sites: {},  // Store website timers
        settings: {
          notifications: true,
          soundAlerts: true,
          autoFocusMode: false,
          defaultLimit: 30  // Default time limit in minutes
        },
        history: {
          daily: {},   // Daily history records
          weekly: {},  // Weekly history records
          monthly: {}  // Monthly history records
        }
      });
    }
  });
});

// Create audio context for notification sounds
let audioContext = null;

// Initialize audio context
function initAudioContext() {
  if (!audioContext) {
    try {
      audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (error) {
      console.error('Failed to create audio context:', error);
    }
  }
  return audioContext;
}

// Play notification sound
function playNotificationSound() {
  try {
    // Make sure the audio context is initialized
    if (!audioContext) {
      audioContext = initAudioContext();
    }
    
    // Make sure audio context is running
    if (audioContext && audioContext.state === 'suspended') {
      audioContext.resume();
    }
    
    if (!audioContext) {
      console.error('Failed to create audio context');
      return false;
    }
    
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.type = 'sine';
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    const now = audioContext.currentTime;
    
    // Play a clear "time's up" sound (two-tone alert)
    oscillator.frequency.setValueAtTime(880, now); // A5
    oscillator.frequency.setValueAtTime(659.25, now + 0.2); // E5
    oscillator.frequency.setValueAtTime(880, now + 0.4); // A5
    
    gainNode.gain.setValueAtTime(0, now);
    gainNode.gain.linearRampToValueAtTime(0.5, now + 0.01);
    gainNode.gain.setValueAtTime(0.5, now + 0.2);
    gainNode.gain.linearRampToValueAtTime(0, now + 0.6);
    
    oscillator.start(now);
    oscillator.stop(now + 0.7);
    
    console.log('Notification sound played');
    return true;
  } catch (error) {
    console.error('Failed to play notification sound:', error);
    
    // Try a simple beep as a last resort
    try {
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgo');
      audio.volume = 0.7;
      audio.play().catch(e => console.error('Failed to play audio beep:', e));
    } catch (e) {
      console.error('Failed to play fallback sound:', e);
    }
    return false;
  }
}

// Track active tab time
let activeTabId = null;
let activeUrl = null;
let startTime = null;
let updateIntervalId = null;

// Listen for tab activation changes
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  const tab = await chrome.tabs.get(activeInfo.tabId);
  handleTabChange(tab);
});

// Listen for tab URL changes
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.url && tabId === activeTabId) {
    handleTabChange(tab);
  }
});

// Listen for browser state changes
chrome.idle.onStateChanged.addListener((state) => {
  if (state === 'active') {
    // Browser is active again, restart tracking
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs.length > 0) {
        handleTabChange(tabs[0]);
      }
    });
  } else {
    // Browser is idle or locked, pause tracking
    if (activeUrl && startTime) {
      saveTime(activeUrl, Date.now() - startTime);
      activeUrl = null;
      startTime = null;
    }
  }
});

// Handle tab changes
async function handleTabChange(tab) {
  // Save time for previous tab if exists
  if (activeUrl && startTime) {
    await saveTime(activeUrl, Date.now() - startTime);
  }

  activeTabId = tab.id;
  activeUrl = getDomain(tab.url);
  startTime = Date.now();

  // Check if we need to track this domain
  chrome.storage.local.get(['sites'], (result) => {
    if (result.sites && result.sites[activeUrl]) {
      checkTimeLimit(activeUrl);
      
      // Start interval to check time limit regularly
      if (updateIntervalId) {
        clearInterval(updateIntervalId);
      }
      
      // Determine check frequency based on how close to limit
      const site = result.sites[activeUrl];
      let checkInterval = 60000; // Default: check every minute
      
      if (site && site.limit) {
        const limitMs = site.limit * 60 * 1000;
        const timeSpent = site.timeSpent || 0;
        const timeRemaining = limitMs - timeSpent;
        
        // If less than 2 minutes remaining, check more frequently
        if (timeRemaining <= 120000) {
          checkInterval = 10000; // Check every 10 seconds when close to limit
        }
      }
      
      updateIntervalId = setInterval(() => {
        checkTimeLimit(activeUrl);
      }, checkInterval);
    }
  });
}

// Save time spent on a website
async function saveTime(domain, timeSpent) {
  chrome.storage.local.get(['sites', 'history'], (result) => {
    const sites = result.sites || {};
    const history = result.history || { daily: {}, weekly: {}, monthly: {} };
    
    // Update current day's time
    if (!sites[domain]) {
      sites[domain] = { timeSpent: 0, limit: null };
    }
    sites[domain].timeSpent += timeSpent;
    
    // Update historical data
    updateHistoricalData(history, domain, timeSpent);
    
    chrome.storage.local.set({ sites, history });
  });
}

// Update historical data for statistics
function updateHistoricalData(history, domain, timeSpent) {
  const now = new Date();
  const dateKey = formatDate(now);
  const weekKey = getWeekNumber(now);
  const monthKey = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
  
  // Update daily history
  if (!history.daily[dateKey]) {
    history.daily[dateKey] = {};
  }
  if (!history.daily[dateKey][domain]) {
    history.daily[dateKey][domain] = 0;
  }
  history.daily[dateKey][domain] += timeSpent;
  
  // Update weekly history
  if (!history.weekly[weekKey]) {
    history.weekly[weekKey] = {};
  }
  if (!history.weekly[weekKey][domain]) {
    history.weekly[weekKey][domain] = 0;
  }
  history.weekly[weekKey][domain] += timeSpent;
  
  // Update monthly history
  if (!history.monthly[monthKey]) {
    history.monthly[monthKey] = {};
  }
  if (!history.monthly[monthKey][domain]) {
    history.monthly[monthKey][domain] = 0;
  }
  history.monthly[monthKey][domain] += timeSpent;
  
  // Cleanup old history (keep only last 30 days, 12 weeks, 12 months)
  cleanupHistory(history);
}

// Check if time limit is exceeded
function checkTimeLimit(domain) {
  chrome.storage.local.get(['sites', 'settings'], (result) => {
    const site = result.sites[domain];
    const settings = result.settings || {};
    
    if (!site || !site.limit) return;
    
    const limitMs = site.limit * 60 * 1000;
    const timeSpent = site.timeSpent || 0;
    const timeRemaining = limitMs - timeSpent;
    
    // If time limit is exceeded
    if (timeSpent >= limitMs) {
      notifyTimeLimit(domain, settings);
      
      // If auto focus mode is enabled, open focus page
      if (settings.autoFocusMode) {
        chrome.tabs.create({ url: 'focus.html' });
      }
      
      // Store the last notification time to prevent spam
      site.lastNotification = Date.now();
      chrome.storage.local.set({ sites: result.sites });
    } 
    // If approaching time limit (within 1 minute), show a warning
    else if (timeRemaining <= 60000 && (!site.lastWarning || Date.now() - site.lastWarning > 60000)) {
      // Show a warning notification
      chrome.notifications.create({
        type: 'basic',
        iconUrl: chrome.runtime.getURL('assets/icons/icon48.png'),
        title: '⏳ Time Limit Approaching',
        message: `You have less than 1 minute remaining on ${domain}`,
        priority: 1
      });
      
      // Play a gentler warning sound
      if (settings.soundAlerts !== false) {
        try {
          const context = initAudioContext();
          if (context) {
            const oscillator = context.createOscillator();
            const gainNode = context.createGain();
            
            oscillator.type = 'sine';
            oscillator.connect(gainNode);
            gainNode.connect(context.destination);
            
            const now = context.currentTime;
            oscillator.frequency.setValueAtTime(440, now);
            gainNode.gain.setValueAtTime(0, now);
            gainNode.gain.linearRampToValueAtTime(0.2, now + 0.1);
            gainNode.gain.linearRampToValueAtTime(0, now + 0.5);
            
            oscillator.start(now);
            oscillator.stop(now + 0.5);
          }
        } catch (error) {
          console.error('Failed to play warning sound:', error);
        }
      }
      
      // Store the last warning time to prevent spam
      site.lastWarning = Date.now();
      chrome.storage.local.set({ sites: result.sites });
    }
  });
}

// Show notification when time limit is reached
function notifyTimeLimit(domain, settings) {
  // Check if we've already shown a notification recently for this domain (within last 60 seconds)
  chrome.storage.local.get(['sites'], (result) => {
    const sites = result.sites || {};
    const site = sites[domain];
    
    if (site && site.lastNotification && Date.now() - site.lastNotification < 60000) {
      // Don't spam notifications, return early
      return;
    }
    
    // Use chrome.runtime.getURL to get the absolute URL for the icon
    const iconUrl = chrome.runtime.getURL('assets/icons/icon48.png');
    
    // Play notification sound if sound alerts are enabled
    if (settings.soundAlerts !== false) {
      playNotificationSound();
    }
    
    // Create the notification with high priority and make it persistent
    chrome.notifications.create(`time-limit-${domain}-${Date.now()}`, {
      type: 'basic',
      iconUrl: iconUrl,
      title: '⏰ Time Limit Reached!',
      message: `You've spent your allocated time on ${domain}. It's time to take a break!`,
      priority: 2, // High priority
      buttons: [
        { title: 'Enter Focus Mode' },
        { title: 'Add 5 Minutes' }
      ],
      requireInteraction: true  // Make notification persistent until user interaction
    }, (createdId) => {
      // Check if notification was created successfully
      if (chrome.runtime.lastError) {
        console.error('Error creating notification:', chrome.runtime.lastError);
        // Try to create a basic notification without buttons as fallback
        chrome.notifications.create(`time-limit-basic-${domain}-${Date.now()}`, {
          type: 'basic',
          iconUrl: iconUrl,
          title: '⏰ Time Limit Reached!',
          message: `You've spent your allocated time on ${domain}. It's time to take a break!`,
          requireInteraction: true
        });
      } else {
        console.log(`Time limit notification sent for ${domain} with ID: ${createdId}`);
      }
    });
    
    // Flash the extension icon to get user attention
    flashExtensionIcon();
    
    // Update last notification time
    if (site) {
      site.lastNotification = Date.now();
      chrome.storage.local.set({ sites });
    }
  });
}

// Flash the extension icon to get user attention
function flashExtensionIcon() {
  const defaultIcon = {
    16: 'assets/icons/icon16.png',
    32: 'assets/icons/icon32.png',
    48: 'assets/icons/icon48.png',
    128: 'assets/icons/icon128.png'
  };
  
  const alertIcon = {
    16: 'assets/icons/icon16.png',
    32: 'assets/icons/icon32.png',
    48: 'assets/icons/icon48.png',
    128: 'assets/icons/icon128.png'
  };
  
  // Flash 3 times
  let flashCount = 0;
  const flashInterval = setInterval(() => {
    chrome.action.setIcon({
      path: flashCount % 2 === 0 ? alertIcon : defaultIcon
    });
    
    flashCount++;
    if (flashCount >= 6) {
      clearInterval(flashInterval);
      chrome.action.setIcon({ path: defaultIcon });
    }
  }, 500);
}

// Handle notification button click
chrome.notifications.onButtonClicked.addListener((notificationId, buttonIndex) => {
  // Extract domain from notification ID if possible
  let domain = '';
  if (notificationId.startsWith('time-limit-')) {
    const parts = notificationId.split('-');
    if (parts.length >= 3) {
      // Try to reconstruct the domain from the notification ID
      domain = parts.slice(2, -1).join('-');
    }
  }
  
  if (buttonIndex === 0) {
    // Button 1: Open focus mode page
    chrome.tabs.create({ url: 'focus.html' });
    chrome.notifications.clear(notificationId);
  } else if (buttonIndex === 1) {
    // Button 2: Add 5 more minutes
    addExtraTime(domain, 5);
    chrome.notifications.clear(notificationId);
    
    // Show confirmation notification
    chrome.notifications.create({
      type: 'basic',
      iconUrl: chrome.runtime.getURL('assets/icons/icon48.png'),
      title: '5 Minutes Added',
      message: domain ? `You have 5 more minutes on ${domain}` : 'You have 5 more minutes',
      priority: 0
    });
  }
});

// Add extra time to a domain's limit
function addExtraTime(domain, minutes) {
  if (!domain) return;
  
  chrome.storage.local.get(['sites'], (result) => {
    const sites = result.sites || {};
    if (sites[domain]) {
      // Subtract 5 minutes worth of milliseconds from the current time spent
      // This effectively gives 5 more minutes before hitting the limit again
      const extraTimeMs = minutes * 60 * 1000;
      sites[domain].timeSpent -= extraTimeMs;
      
      // Ensure time spent doesn't go below zero
      if (sites[domain].timeSpent < 0) {
        sites[domain].timeSpent = 0;
      }
      
      chrome.storage.local.set({ sites });
      console.log(`Added ${minutes} minutes to ${domain}`);
    }
  });
}

// Reset timers at midnight
chrome.alarms.create('resetTimer', {
  when: getNextMidnight(),
  periodInMinutes: 24 * 60
});

chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'resetTimer') {
    resetDailyTimers();
  }
});

// Helper functions
function getDomain(url) {
  if (!url) return '';
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    console.error('Invalid URL:', url);
    return '';
  }
}

function getNextMidnight() {
  const now = new Date();
  const midnight = new Date(now);
  midnight.setHours(24, 0, 0, 0);
  return midnight.getTime();
}

function resetDailyTimers() {
  chrome.storage.local.get(['sites'], (result) => {
    const sites = result.sites;
    for (let domain in sites) {
      sites[domain].timeSpent = 0;
    }
    chrome.storage.local.set({ sites });
  });
}

// Format date as YYYY-MM-DD
function formatDate(date) {
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
}

// Get ISO week number (YYYY-WW)
function getWeekNumber(date) {
  const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay() || 7));
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  const weekNo = Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  return `${d.getUTCFullYear()}-${weekNo.toString().padStart(2, '0')}`;
}

// Cleanup old history records
function cleanupHistory(history) {
  const now = new Date();
  
  // Keep only last 30 days
  const dailyEntries = Object.keys(history.daily)
    .sort()
    .reverse()
    .slice(0, 30);
  
  const newDaily = {};
  dailyEntries.forEach(key => {
    newDaily[key] = history.daily[key];
  });
  history.daily = newDaily;
  
  // Keep only last 12 weeks
  const weeklyEntries = Object.keys(history.weekly)
    .sort()
    .reverse()
    .slice(0, 12);
  
  const newWeekly = {};
  weeklyEntries.forEach(key => {
    newWeekly[key] = history.weekly[key];
  });
  history.weekly = newWeekly;
  
  // Keep only last 12 months
  const monthlyEntries = Object.keys(history.monthly)
    .sort()
    .reverse()
    .slice(0, 12);
  
  const newMonthly = {};
  monthlyEntries.forEach(key => {
    newMonthly[key] = history.monthly[key];
  });
  history.monthly = newMonthly;
}