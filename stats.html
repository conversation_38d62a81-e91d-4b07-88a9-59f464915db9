<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Usage Statistics - Auto Page Timer</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
      --primary-color: #6366f1;
      --primary-hover: #4f46e5;
      --secondary-color: #64748b;
      --secondary-hover: #475569;
      --background: #f8fafc;
      --card-bg: #ffffff;
      --text-primary: #1e293b;
      --text-secondary: #64748b;
      --success: #22c55e;
      --warning: #f59e0b;
      --danger: #ef4444;
      --border-radius: 12px;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
      --transition: all 0.3s ease;
      --gradient: linear-gradient(135deg, #6366f1, #8b5cf6);
    }

    /* Dark mode variables */
    @media (prefers-color-scheme: dark) {
      :root {
        --background: #0f172a;
        --card-bg: #1e293b;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5);
      }
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', sans-serif;
      background: var(--background);
      color: var(--text-primary);
      line-height: 1.5;
      padding: 2rem;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
    }

    header {
      margin-bottom: 2rem;
      text-align: center;
    }

    h1 {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      background: var(--gradient);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }

    .subtitle {
      color: var(--text-secondary);
      font-size: 1rem;
    }

    .stats-container {
      display: grid;
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .card {
      background: var(--card-bg);
      border-radius: var(--border-radius);
      box-shadow: var(--shadow-md);
      overflow: hidden;
    }

    .card-header {
      padding: 1.25rem;
      background: var(--gradient);
      color: white;
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-body {
      padding: 1.5rem;
    }

    .summary-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .stat-item {
      background: rgba(99, 102, 241, 0.05);
      padding: 1rem;
      border-radius: var(--border-radius);
      text-align: center;
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .site-list {
      margin-top: 1.5rem;
    }

    .site-item {
      display: grid;
      grid-template-columns: 1fr auto auto;
      gap: 1rem;
      padding: 1rem;
      border-bottom: 1px solid rgba(99, 102, 241, 0.1);
      align-items: center;
    }

    .site-item:last-child {
      border-bottom: none;
    }

    .site-domain {
      font-weight: 500;
    }

    .site-time {
      text-align: right;
      font-variant-numeric: tabular-nums;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 6px;
    }

    .status-within {
      background-color: var(--success);
    }

    .status-close {
      background-color: var(--warning);
    }

    .status-over {
      background-color: var(--danger);
    }

    .site-status {
      padding: 0.25rem 0.75rem;
      border-radius: 100px;
      font-size: 0.75rem;
      font-weight: 600;
      display: inline-flex;
      align-items: center;
    }

    .status-within {
      background-color: rgba(34, 197, 94, 0.1);
      color: var(--success);
    }

    .status-close {
      background-color: rgba(245, 158, 11, 0.1);
      color: var(--warning);
    }

    .status-over {
      background-color: rgba(239, 68, 68, 0.1);
      color: var(--danger);
    }

    .no-data {
      text-align: center;
      padding: 2rem;
      color: var(--text-secondary);
    }

    .chart-container {
      height: 300px;
      position: relative;
      margin-top: 1.5rem;
    }

    .tips-list {
      list-style-type: none;
      margin-top: 1rem;
    }

    .tips-list li {
      margin-bottom: 0.75rem;
      padding-left: 1.5rem;
      position: relative;
    }

    .tips-list li::before {
      content: "•";
      position: absolute;
      left: 0;
      color: var(--primary-color);
      font-weight: bold;
    }

    .date-selector {
      display: flex;
      justify-content: center;
      margin-bottom: 1.5rem;
    }

    .date-selector button {
      background: none;
      border: none;
      padding: 0.5rem 1rem;
      cursor: pointer;
      color: var(--text-secondary);
      font-weight: 500;
      border-radius: var(--border-radius);
      transition: var(--transition);
    }

    .date-selector button:hover {
      color: var(--primary-color);
    }

    .date-selector button.active {
      background: rgba(99, 102, 241, 0.1);
      color: var(--primary-color);
    }

    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }

      .summary-stats {
        grid-template-columns: 1fr;
      }

      .site-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .site-time, .site-status {
        text-align: left;
      }
    }
    
    /* Loading state */
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s, visibility 0.3s;
    }
    
    body.loading .loading-overlay {
      opacity: 1;
      visibility: visible;
    }
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="loading-overlay">
    <div class="spinner"></div>
  </div>
  <div class="container">
    <header>
      <h1>Your Browsing Statistics</h1>
      <p class="subtitle">Track your online time and focus habits</p>
    </header>

    <div class="date-selector">
      <button class="active" data-period="today">Today</button>
      <button data-period="week">This Week</button>
      <button data-period="month">This Month</button>
    </div>

    <div class="stats-container">
      <div class="card">
        <div class="card-header">
          <span>Summary</span>
          <span id="date-range">Today</span>
        </div>
        <div class="card-body">
          <div class="summary-stats">
            <div class="stat-item">
              <div class="stat-value" id="total-time">0:00</div>
              <div class="stat-label">Total Time</div>
            </div>
            <div class="stat-item">
              <div class="stat-value" id="sites-visited">0</div>
              <div class="stat-label">Sites Visited</div>
            </div>
            <div class="stat-item">
              <div class="stat-value" id="sites-over-limit">0</div>
              <div class="stat-label">Sites Over Limit</div>
            </div>
          </div>

          <div class="chart-container">
            <canvas id="time-chart"></canvas>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <span>Website Details</span>
        </div>
        <div class="card-body">
          <div class="site-list" id="site-list">
            <!-- Site items will be added here dynamically -->
            <div class="no-data">No data available for the selected period</div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <span>Focus Tips</span>
        </div>
        <div class="card-body">
          <p>Based on your browsing habits, here are some tips to improve your focus:</p>
          <ul class="tips-list" id="tips-list">
            <li>Try using the Pomodoro technique (25 min work, 5 min break) for better focus.</li>
            <li>Consider setting stricter time limits for entertainment sites.</li>
            <li>Use ambient sounds in Focus Mode to block distractions.</li>
            <li>Take regular breaks to maintain productivity throughout the day.</li>
            <li>Try to batch similar tasks together for better efficiency.</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <script src="assets/js/chart.js"></script>
  <script src="stats.js"></script>
</body>
</html> 