<!DOCTYPE html>
<html>
<head>
  <title>Focus Recovery Mode</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background: linear-gradient(135deg, #1e3c72, #2a5298);
      color: white;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .container {
      max-width: 600px;
      width: 90%;
      text-align: center;
      padding: 20px;
    }

    h1 {
      font-size: 2.5em;
      margin-bottom: 20px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
    }

    .timer-container {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      padding: 30px;
      margin: 20px 0;
      backdrop-filter: blur(10px);
    }

    #timer {
      font-size: 5em;
      font-weight: bold;
      margin: 20px 0;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
    }

    .controls {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin: 20px 0;
    }

    button {
      padding: 12px 24px;
      border: none;
      border-radius: 30px;
      font-size: 1em;
      cursor: pointer;
      transition: all 0.3s ease;
      background: white;
      color: #1e3c72;
      font-weight: bold;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .sound-controls {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px;
      margin: 30px 0;
    }

    .sound-btn {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      padding: 15px;
      border-radius: 15px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      position: relative;
      overflow: hidden;
    }

    .sound-btn.active {
      background: rgba(255, 255, 255, 0.3);
    }

    .sound-btn.active::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% {
        transform: scale(0.95);
        opacity: 0.7;
      }
      70% {
        transform: scale(1.05);
        opacity: 0.3;
      }
      100% {
        transform: scale(0.95);
        opacity: 0.7;
      }
    }

    .sound-icon {
      font-size: 2em;
    }

    .quote-container {
      margin-top: 30px;
      padding: 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      backdrop-filter: blur(5px);
    }

    #quote {
      font-style: italic;
      font-size: 1.2em;
      line-height: 1.6;
    }

    .progress-bar {
      width: 100%;
      height: 10px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 5px;
      margin-top: 20px;
      overflow: hidden;
    }

    #progress {
      width: 0%;
      height: 100%;
      background: #4CAF50;
      transition: width 1s linear;
    }

    .status {
      margin-top: 10px;
      font-size: 0.9em;
      opacity: 0.8;
    }

    @media (max-width: 480px) {
      .sound-controls {
        grid-template-columns: 1fr;
      }

      #timer {
        font-size: 4em;
      }

      .controls {
        flex-direction: column;
      }

      button {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Focus Recovery Mode</h1>

    <div class="timer-container">
      <div id="timer">25:00</div>
      <div class="progress-bar">
        <div id="progress"></div>
      </div>
      <div class="status" id="status">Focus Time</div>
    </div>

    <div class="controls">
      <button id="start">Start</button>
      <button id="pause">Pause</button>
      <button id="reset">Reset</button>
    </div>

    <div class="sound-controls">
      <button class="sound-btn" data-sound="rain" aria-label="Play rain sound">
        <span class="sound-icon">🌧️</span>
        Rain
      </button>
      <button class="sound-btn" data-sound="forest" aria-label="Play forest sound">
        <span class="sound-icon">🌳</span>
        Forest
      </button>
      <button class="sound-btn" data-sound="waves" aria-label="Play waves sound">
        <span class="sound-icon">🌊</span>
        Waves
      </button>
    </div>

    <div class="quote-container">
      <p id="quote">"Focus on being productive instead of busy."</p>
    </div>
  </div>

  <script src="focus.js"></script>
</body>
</html>