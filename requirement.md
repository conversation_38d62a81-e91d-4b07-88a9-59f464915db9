🧾 Project Requirements Document
📛 Project Name:
Auto Page Timer with Focus Mode

📂 Type:
Chrome Extension (Frontend: HTML, CSS, JavaScript)
Backend: No need for Node.js, everything can run locally inside the browser.

🎯 Objective (What it does)
The extension helps users become more productive and mindful by:

Tracking how long they spend on each website

Alerting them when their set time limit for a site is reached

Providing a “Focus Recovery Mode” with calming sounds and a Pomodoro timer
It encourages healthier digital habits without being aggressive like website blockers.

🧩 Main Features
1. ⏳ Website Time Tracker
Monitors how much time is spent on each site (e.g., youtube.com, docs.google.com)

Resets or continues based on browser activity

Stores data in chrome.storage.local

2. 🔔 Custom Time Limit Alerts
User can set time limits per domain (e.g., 20 mins for YouTube)

When limit is reached:

A notification popup appears

Optional sound alert

3. 🧘 Focus Recovery Mode
Activates when user exceeds time limit

Offers:

Pomodoro Timer (25 min focus / 5 min break)

Ambient sound options (rain, forest, etc.)

Motivational quote or message

4. 📊 Daily Usage Stats (Optional Phase)
Displays a dashboard of:

Time spent today/this week per site

Time over the limit

Allows user to reset or adjust limits

5. ⚙️ Settings/Options Page
Add domains and set limits

Toggle ambient sound or Pomodoro mode

Set default focus mode behavior

🧠 How the Extension Works (Step-by-Step)
➤ Step 1: Install Extension
User installs from Chrome Web Store or loads unpacked version.

➤ Step 2: Configure Sites and Limits
From the options page, user:

Adds specific websites (e.g., youtube.com)

Sets daily time limits (e.g., 30 mins)

➤ Step 3: Monitor Time
When the user browses a tracked site:

A background script records time spent

It pauses counting when tab is not active or browser is closed

➤ Step 4: Alert and Focus Mode
When time runs out:

A Chrome notification pops up with an alert

Focus Recovery Mode becomes available via popup or auto-launch

If enabled, Pomodoro + ambient audio starts

➤ Step 5: View Usage
User can open the popup to:

Check how much time they’ve used

Start/restart the timer

Enter Focus Mode manually

📁 Folder & File Structure
pgsql
Copy
Edit
auto-focus-extension/
│
├── manifest.json
├── background.js              # Time tracking, tab monitoring
├── popup.html                 # Timer, quick controls
├── popup.js
├── options.html               # Settings page (add sites, set limits)
├── options.js
├── focus.html                 # Focus recovery UI (timer + sounds)
├── focus.js
├── style.css
└── assets/
    ├── sounds/                # Ambient audio files
    └── icons/                 # Extension icons
🧩 Chrome APIs Used
Feature	API
Track tab URL & activity	chrome.tabs, chrome.runtime, chrome.idle
Store user settings	chrome.storage.local
Set up time checks	chrome.alarms
Notifications	chrome.notifications
Popup/Options UI	HTML + JS

💡 Optional Enhancements (Future Ideas)
Sync time limits across devices (chrome.storage.sync)

Add dark/light theme for focus UI

Integrate with Google Calendar for blocking during meetings

Auto-reset limits at midnight