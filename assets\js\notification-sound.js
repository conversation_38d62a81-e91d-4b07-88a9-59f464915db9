// Notification Sound Player
(function() {
  // Create audio context
  let audioContext = null;
  
  try {
    audioContext = new (window.AudioContext || window.webkitAudioContext)();
  } catch (error) {
    console.error('Web Audio API not supported:', error);
    return;
  }
  
  // Function to play notification sound
  window.playNotificationSound = function() {
    try {
      if (audioContext.state === 'suspended') {
        audioContext.resume();
      }
      
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.type = 'sine';
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      const now = audioContext.currentTime;
      
      // Play a clear "time's up" sound (two-tone alert)
      oscillator.frequency.setValueAtTime(880, now); // A5
      oscillator.frequency.setValueAtTime(659.25, now + 0.2); // E5
      oscillator.frequency.setValueAtTime(880, now + 0.4); // A5
      
      gainNode.gain.setValueAtTime(0, now);
      gainNode.gain.linearRampToValueAtTime(0.5, now + 0.01);
      gainNode.gain.setValueAtTime(0.5, now + 0.2);
      gainNode.gain.linearRampToValueAtTime(0, now + 0.6);
      
      oscillator.start(now);
      oscillator.stop(now + 0.7);
      
      return true;
    } catch (error) {
      console.error('Failed to play notification sound:', error);
      return false;
    }
  };
})(); 