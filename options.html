<!DOCTYPE html>
<html>
<head>
  <title>Auto Page Timer Settings</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f8f9fa;
      color: #343a40;
    }

    .container {
      background-color: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    h1 {
      color: #2c3e50;
      margin-bottom: 24px;
      text-align: center;
    }

    .section {
      margin-bottom: 32px;
    }

    h2 {
      color: #495057;
      font-size: 20px;
      margin-bottom: 16px;
    }

    .site-list {
      margin-bottom: 24px;
    }

    .site-item {
      display: grid;
      grid-template-columns: 2fr 1fr auto;
      gap: 16px;
      align-items: center;
      padding: 12px;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      margin-bottom: 8px;
    }

    .site-domain {
      font-weight: 500;
    }

    .site-limit {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    input[type="number"] {
      width: 80px;
      padding: 6px;
      border: 1px solid #ced4da;
      border-radius: 4px;
    }

    .remove-btn {
      background-color: #dc3545;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
    }

    .remove-btn:hover {
      background-color: #c82333;
    }

    .add-site {
      display: grid;
      grid-template-columns: 2fr 1fr auto;
      gap: 16px;
      align-items: center;
      margin-bottom: 24px;
    }

    input[type="text"] {
      padding: 8px;
      border: 1px solid #ced4da;
      border-radius: 4px;
    }

    .add-btn {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }

    .add-btn:hover {
      background-color: #43a047;
    }

    .settings-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }

    .setting-item {
      background-color: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
    }

    .setting-item label {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .setting-item p {
      color: #6c757d;
      font-size: 14px;
      margin: 0;
    }

    .save-btn {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      width: 100%;
      margin-top: 24px;
    }

    .save-btn:hover {
      background-color: #43a047;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Auto Page Timer Settings</h1>

    <div class="section">
      <h2>Managed Sites</h2>
      <div class="site-list" id="site-list">
        <!-- Site items will be added here dynamically -->
      </div>

      <div class="add-site">
        <input type="text" id="new-site" placeholder="Enter domain (e.g., youtube.com)">
        <input type="number" id="new-limit" placeholder="Minutes">
        <button class="add-btn" id="add-site">Add Site</button>
      </div>
    </div>

    <div class="section">
      <h2>General Settings</h2>
      <div class="settings-grid">
        <div class="setting-item">
          <label>
            <input type="checkbox" id="notifications">
            Enable Notifications
          </label>
          <p>Get alerts when you reach time limits</p>
        </div>

        <div class="setting-item">
          <label>
            <input type="checkbox" id="sound-alerts">
            Sound Alerts
          </label>
          <p>Play a sound with notifications</p>
        </div>

        <div class="setting-item">
          <label>
            <input type="checkbox" id="auto-focus">
            Auto Focus Mode
          </label>
          <p>Automatically enter focus mode when limit is reached</p>
        </div>

        <div class="setting-item">
          <label>
            Default Time Limit
            <input type="number" id="default-limit" min="1" style="width: 80px;">
          </label>
          <p>Minutes to apply for new sites</p>
        </div>
      </div>
    </div>

    <button class="save-btn" id="save-settings">Save Settings</button>
  </div>

  <script src="options.js"></script>
</body>
</html>