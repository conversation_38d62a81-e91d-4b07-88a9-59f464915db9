// DOM Elements
const siteList = document.getElementById('site-list');
const newSiteInput = document.getElementById('new-site');
const newLimitInput = document.getElementById('new-limit');
const addSiteBtn = document.getElementById('add-site');
const notificationsCheckbox = document.getElementById('notifications');
const soundAlertsCheckbox = document.getElementById('sound-alerts');
const autoFocusCheckbox = document.getElementById('auto-focus');
const defaultLimitInput = document.getElementById('default-limit');
const saveSettingsBtn = document.getElementById('save-settings');

// Load settings
function loadSettings() {
  chrome.storage.local.get(['sites', 'settings'], (result) => {
    const { sites = {}, settings = {} } = result;
    
    // Load sites
    renderSiteList(sites);
    
    // Load general settings
    notificationsCheckbox.checked = settings.notifications ?? true;
    soundAlertsCheckbox.checked = settings.soundAlerts ?? true;
    autoFocusCheckbox.checked = settings.autoFocusMode ?? false;
    defaultLimitInput.value = settings.defaultLimit ?? 30;
  });
}

// Render site list
function renderSiteList(sites) {
  siteList.innerHTML = '';
  
  Object.entries(sites).forEach(([domain, data]) => {
    const siteItem = document.createElement('div');
    siteItem.className = 'site-item';
    siteItem.innerHTML = `
      <div class="site-domain">${domain}</div>
      <div class="site-limit">
        <input type="number" value="${data.limit || ''}" 
               min="1" placeholder="Minutes"
               data-domain="${domain}">
        minutes
      </div>
      <button class="remove-btn" data-domain="${domain}">Remove</button>
    `;
    siteList.appendChild(siteItem);

    // Add event listeners for limit changes
    const limitInput = siteItem.querySelector('input[type="number"]');
    limitInput.addEventListener('change', (e) => {
      updateSiteLimit(domain, parseInt(e.target.value));
    });

    // Add event listener for remove button
    const removeBtn = siteItem.querySelector('.remove-btn');
    removeBtn.addEventListener('click', () => {
      removeSite(domain);
    });
  });
}

// Add new site
addSiteBtn.addEventListener('click', () => {
  const domain = newSiteInput.value.trim().toLowerCase();
  const limit = parseInt(newLimitInput.value);

  if (!domain) {
    alert('Please enter a domain');
    return;
  }

  if (isNaN(limit) || limit <= 0) {
    alert('Please enter a valid time limit');
    return;
  }

  chrome.storage.local.get(['sites'], (result) => {
    const sites = result.sites || {};
    
    if (sites[domain]) {
      alert('This domain is already being tracked');
      return;
    }

    sites[domain] = {
      timeSpent: 0,
      limit: limit
    };

    chrome.storage.local.set({ sites }, () => {
      renderSiteList(sites);
      newSiteInput.value = '';
      newLimitInput.value = '';
    });
  });
});

// Update site limit
function updateSiteLimit(domain, limit) {
  chrome.storage.local.get(['sites'], (result) => {
    const sites = result.sites || {};
    if (sites[domain]) {
      sites[domain].limit = limit;
      chrome.storage.local.set({ sites });
    }
  });
}

// Remove site
function removeSite(domain) {
  if (confirm(`Are you sure you want to remove ${domain}?`)) {
    chrome.storage.local.get(['sites'], (result) => {
      const sites = result.sites || {};
      delete sites[domain];
      chrome.storage.local.set({ sites }, () => {
        renderSiteList(sites);
      });
    });
  }
}

// Save general settings
saveSettingsBtn.addEventListener('click', () => {
  const settings = {
    notifications: notificationsCheckbox.checked,
    soundAlerts: soundAlertsCheckbox.checked,
    autoFocusMode: autoFocusCheckbox.checked,
    defaultLimit: parseInt(defaultLimitInput.value) || 30
  };

  chrome.storage.local.set({ settings }, () => {
    alert('Settings saved successfully!');
  });
});

// Domain validation helper
newSiteInput.addEventListener('input', (e) => {
  let value = e.target.value.toLowerCase();
  
  // Remove protocol if present
  value = value.replace(/^https?:\/\//i, '');
  
  // Remove www. if present
  value = value.replace(/^www\./i, '');
  
  // Remove paths
  value = value.split('/')[0];
  
  e.target.value = value;
});

// Initialize settings when page loads
document.addEventListener('DOMContentLoaded', loadSettings);