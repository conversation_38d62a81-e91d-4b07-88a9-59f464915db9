<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Auto Page Timer</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <header class="header">
      <div class="site-info">
        <span class="label">Current Site</span>
        <span id="current-domain" class="domain">-</span>
      </div>
    </header>

    <main class="main-content">
      <section class="time-section" aria-labelledby="time-section-title">
        <h2 id="time-section-title" class="visually-hidden">Time Tracking</h2>
        
        <div class="time-display">
          <div class="counter-wrapper">
            <span id="time-counter" class="counter" role="timer" aria-label="Time spent">00:00:00</span>
          </div>
          
          <div class="limit-wrapper">
            <span class="label">Daily Limit</span>
            <span id="time-limit-display">Not Set</span>
          </div>
        </div>

        <div class="progress-wrapper" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0">
          <div id="time-progress" class="progress-bar"></div>
        </div>
      </section>

      <section class="actions-section">
        <button id="focus-mode" class="btn primary" aria-expanded="false">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
            <path d="M18.7 12.4c-.28-.16-.57-.29-.86-.4.29-.11.58-.24.86-.4 1.92-1.11 2.99-3.12 3-5.19-1.79-1.03-4.07-1.11-6 0-.28.16-.54.35-.78.54.05-.31.08-.63.08-.95 0-2.22-1.21-4.15-3-5.19C10.21 1.85 9 3.78 9 6c0 .32.03.64.08.95-.24-.2-.5-.39-.78-.55-1.92-1.11-4.2-1.03-6 0 0 2.07 1.07 4.08 3 5.19.28.16.57.29.86.4-.29.11-.58.24-.86.4-1.92 1.11-2.99 3.12-3 5.19 1.79 1.03 4.07 1.11 6 0 .28-.16.54-.35.78-.54-.05.32-.08.64-.08.96 0 2.22 1.21 4.15 3 5.19 1.79-1.04 3-2.97 3-5.19 0-.32-.03-.64-.08-.95.24.2.5.38.78.54 1.92 1.11 4.2 1.03 6 0-.01-2.07-1.08-4.08-3-5.19z"/>
          </svg>
          Focus Mode
        </button>
        
        <button id="set-limit" class="btn secondary">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <path d="M12 6v6l4 2"/>
          </svg>
          Set Limit
        </button>
        
        <button id="view-stats" class="btn secondary">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M18 20V10"></path>
            <path d="M12 20V4"></path>
            <path d="M6 20v-6"></path>
          </svg>
          Stats
        </button>
      </section>

      <section id="focus-mode-panel" class="focus-panel hidden" aria-hidden="true">
        <div class="pomodoro-section">
          <h3>Pomodoro Timer</h3>
          <div class="timer-display">
            <span id="pomodoro-timer" role="timer" aria-label="Pomodoro timer">25:00</span>
          </div>
          <button id="start-pomodoro" class="btn primary">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
            Start Pomodoro
          </button>
        </div>

        <div class="sounds-section">
          <h3>Ambient Sounds</h3>
          <div class="sound-buttons">
            <button class="sound-btn" data-sound="rain" aria-label="Play rain sound">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 16.2A6 6 0 0 0 16 8h-3.8a8 8 0 1 0-1.2 15.9h9a4 4 0 0 0 0-7.7z"></path>
              </svg>
              Rain
            </button>
            
            <button class="sound-btn" data-sound="forest" aria-label="Play forest sound">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M16 18v-3a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v3"></path>
                <path d="M15 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                <path d="M9 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                <path d="M19 16a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                <path d="M5 16a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                <path d="M12 4a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"></path>
                <path d="M12 20v-4"></path>
              </svg>
              Forest
            </button>
            
            <button class="sound-btn" data-sound="waves" aria-label="Play waves sound">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M2 12h6a2 2 0 1 0 0-4H2v4z"></path>
                <path d="M8 12h6a2 2 0 1 1 0 4H2v-4"></path>
                <path d="M14 12h6a2 2 0 1 1 0 4h-6a2 2 0 1 0 0-4z"></path>
                <path d="M20 12h-6a2 2 0 1 1 0-4h6a2 2 0 1 0 0 4z"></path>
              </svg>
              Waves
            </button>
          </div>
        </div>

        <div class="motivation-section">
          <blockquote id="motivation-quote" class="quote" aria-live="polite">
            "Focus on being productive instead of busy."
          </blockquote>
        </div>
      </section>
    </main>
  </div>

  <script src="popup.js"></script>
</body>
</html>